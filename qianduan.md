# 前端开发计划文档

## 📋 项目概述

**项目名称**: Solve Web 管理系统  
**前端框架**: Soybean Admin (Vue3 + TypeScript + Vite + Naive UI)  
**后端API**: Go + Gin + MySQL + Redis  
**文档版本**: v2.0  
**更新时间**: 2025-06-19 21:59:38 CST  

## 🎯 开发目标

构建一个完整的用户认证系统，包括：
- 用户注册（需管理员审核）
- 用户登录（支持防爆破）
- 密码管理（忘记密码、修改密码）
- 短信验证码（统一接口，防爆破）
- 用户状态管理（正常/禁用/审核中）

## 🏗️ 技术架构

### 前端技术栈
- **框架**: Vue 3.x + Composition API
- **语言**: TypeScript 5.x
- **构建工具**: Vite 5.x
- **UI组件**: Naive UI
- **状态管理**: Pinia
- **路由**: Vue Router 4.x
- **HTTP客户端**: @sa/axios
- **样式**: UnoCSS

### 后端技术栈
- **语言**: Go 1.21+
- **框架**: Gin
- **数据库**: MySQL 8.0+
- **缓存**: Redis 7.0+
- **认证**: JWT
- **短信**: 阿里云SMS

## 📊 用户状态说明

| 状态值 | 状态名称 | 说明 | 登录权限 | 前端处理 |
|--------|----------|------|----------|----------|
| 0 | 禁用 | 用户被管理员禁用 | ❌ 无法登录 | 显示禁用提示 |
| 1 | 正常 | 用户正常状态 | ✅ 可以登录 | 正常使用系统 |
| 2 | 审核中 | 新注册用户等待审核 | ❌ 无法登录 | 显示审核提示 |

## 🔐 安全机制

### 登录防爆破
- **触发条件**: 连续5次密码错误
- **冻结时间**: 24小时
- **解冻方式**: 自动解冻 或 重置密码
- **前端处理**: 显示冻结剩余时间

### 短信验证码防爆破
- **触发条件**: 连续5次验证码错误
- **冻结时间**: 24小时
- **影响功能**: 忘记密码、重置密码、修改密码
- **前端处理**: 禁用发送按钮，显示冻结提示

### 验证码安全
- **有效期**: 5分钟
- **存储**: Redis
- **验证后**: 自动删除
- **开发环境**: 返回验证码（测试用）
- **生产环境**: 不返回验证码

## 🌐 API 接口规范

### 基础信息
- **Base URL**: `http://localhost:8080/api/v1` (开发) / `https://api.yourdomain.com/api/v1` (生产)
- **Content-Type**: `application/json`
- **认证方式**: `Authorization: Bearer <JWT_TOKEN>`

### 统一响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```

### 错误码规范
| 错误码 | 说明 | 前端处理 |
|--------|------|----------|
| 200 | 成功 | 正常处理 |
| 400 | 请求参数错误 | 显示错误信息 |
| 401 | 未授权/认证失败 | 跳转登录页 |
| 403 | 权限不足 | 显示权限不足提示 |
| 404 | 资源不存在 | 显示404页面 |
| 500 | 服务器内部错误 | 显示系统错误提示 |

## 📱 核心API接口

### 1. 短信验证码接口

#### 1.1 发送短信验证码（推荐）
```typescript
// 接口地址
POST /send-sms

// 请求参数
interface SendSmsRequest {
  phone: string;           // 手机号（11位）
  type: 'register' | 'reset'; // 验证码类型
}

// 响应数据
interface SendSmsResponse {
  code: number;
  message: string;
  data: {
    code?: string;         // 仅开发环境返回
  };
}

// 使用示例
const sendSms = async (phone: string, type: 'register' | 'reset') => {
  return await authApi.sendSms({ phone, type });
};
```

#### 1.2 发送修改密码验证码
```typescript
// 接口地址（需JWT认证）
POST /user/send-change-password-sms

// 无需参数，从JWT中获取用户信息
const sendChangePasswordSms = async () => {
  return await authApi.sendChangePasswordSms();
};
```

### 2. 用户注册接口

```typescript
// 接口地址
POST /register

// 请求参数
interface RegisterRequest {
  phone: string;           // 手机号
  password: string;        // 密码（最少6位）
  nickname: string;        // 用户昵称
  sms_code: string;        // 短信验证码
}

// 响应数据
interface RegisterResponse {
  id: number;
  phone: string;
  nickname: string;
  is_active: 0 | 1 | 2;   // 用户状态
  status: string;          // 状态描述
}

// 使用示例
const register = async (data: RegisterRequest) => {
  return await authApi.register(data);
};
```

### 3. 用户登录接口

```typescript
// 接口地址
POST /login

// 请求参数
interface LoginRequest {
  phone: string;           // 手机号
  password: string;        // 密码
}

// 响应数据
interface LoginResponse {
  token: string;           // JWT令牌
  user: {
    id: number;
    phone: string;
    nickname: string;
    role: 'admin' | 'manager' | 'user';
    balance: number;
    is_active: 0 | 1 | 2;
  };
}

// 使用示例
const login = async (data: LoginRequest) => {
  return await authApi.login(data);
};
```

### 4. 忘记密码接口

#### 4.1 第一步：获取重置令牌
```typescript
// 接口地址
POST /forgot-password

// 请求参数
interface ForgotPasswordRequest {
  phone: string;           // 手机号
  sms_code: string;        // 短信验证码
}

// 响应数据
interface ForgotPasswordResponse {
  token: string;           // 重置令牌（30分钟有效）
}
```

#### 4.2 第二步：重置密码
```typescript
// 接口地址
POST /reset-password

// 请求参数
interface ResetPasswordRequest {
  token: string;           // 重置令牌
  new_password: string;    // 新密码（最少6位）
  sms_code: string;        // 短信验证码（需重新获取）
}
```

### 5. 修改密码接口

```typescript
// 接口地址（需JWT认证）
POST /user/change-password

// 请求参数
interface ChangePasswordRequest {
  old_password: string;    // 原密码
  new_password: string;    // 新密码（最少6位）
  sms_code: string;        // 短信验证码
}
```

### 6. 用户信息接口

#### 6.1 获取用户信息
```typescript
// 接口地址（需JWT认证）
GET /user/profile

// 响应数据
interface UserProfile {
  id: number;
  phone: string;
  nickname: string;
  role: string;
  balance: number;
  is_active: 0 | 1 | 2;
  created_at: string;
}
```

#### 6.2 更新用户信息
```typescript
// 接口地址（需JWT认证）
PUT /user/profile

// 请求参数
interface UpdateProfileRequest {
  nickname: string;        // 用户昵称
}
```

## 🔧 Soybean Admin 集成指南

### 1. 环境配置

#### 1.1 环境变量配置
```bash
# .env.development
VITE_SERVICE_BASE_URL=http://localhost:8080/api/v1
VITE_OTHER_SERVICE_BASE_URL=http://localhost:3001

# .env.production
VITE_SERVICE_BASE_URL=https://api.yourdomain.com/api/v1
VITE_OTHER_SERVICE_BASE_URL=https://api.yourdomain.com
```

#### 1.2 Vite配置
```typescript
// vite.config.ts
export default defineConfig({
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '/api')
      }
    }
  }
});
```

### 2. HTTP请求配置

#### 2.1 请求拦截器配置
```typescript
// src/service/request/index.ts
import { createRequest } from '@sa/axios';
import { localStg } from '@/utils/storage';
import { $t } from '@/locales';

const { instance } = createRequest({
  baseURL: import.meta.env.VITE_SERVICE_BASE_URL,
  timeout: 10000
});

// 请求拦截器
instance.interceptors.request.use(
  config => {
    const token = localStg.get('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => Promise.reject(error)
);

// 响应拦截器
instance.interceptors.response.use(
  response => {
    const { code, message, data } = response.data;
    
    if (code === 200) {
      return data;
    }
    
    // 处理认证错误
    if (code === 401) {
      localStg.remove('token');
      localStg.remove('userInfo');
      window.location.href = '/login';
      return Promise.reject(new Error(message));
    }
    
    // 处理其他错误
    window.$message?.error(message);
    return Promise.reject(new Error(message));
  },
  error => {
    window.$message?.error($t('common.requestFailed'));
    return Promise.reject(error);
  }
);

export { instance as request };
```

### 3. TypeScript类型定义

#### 3.1 API类型定义
```typescript
// src/typings/api.d.ts
declare namespace Api {
  namespace Auth {
    // 用户信息
    interface UserInfo {
      id: number;
      phone: string;
      nickname: string;
      role: 'admin' | 'manager' | 'user';
      balance: number;
      is_active: 0 | 1 | 2;
      created_at: string;
    }

    // 短信验证码请求
    interface SmsRequest {
      phone: string;
      type: 'register' | 'reset';
    }

    // 登录请求
    interface LoginRequest {
      phone: string;
      password: string;
    }

    // 登录响应
    interface LoginResponse {
      token: string;
      user: UserInfo;
    }

    // 注册请求
    interface RegisterRequest {
      phone: string;
      password: string;
      nickname: string;
      sms_code: string;
    }

    // 忘记密码请求
    interface ForgotPasswordRequest {
      phone: string;
      sms_code: string;
    }

    // 重置密码请求
    interface ResetPasswordRequest {
      token: string;
      new_password: string;
      sms_code: string;
    }

    // 修改密码请求
    interface ChangePasswordRequest {
      old_password: string;
      new_password: string;
      sms_code: string;
    }
  }

  // 统一响应格式
  interface Response<T = any> {
    code: number;
    message: string;
    data: T;
  }
}
```

### 4. API服务封装

#### 4.1 认证API服务
```typescript
// src/service/api/auth.ts
import { request } from '../request';

export const authApi = {
  // 发送短信验证码
  sendSms: (data: Api.Auth.SmsRequest) => 
    request.post<Api.Response>('/send-sms', data),

  // 发送修改密码验证码
  sendChangePasswordSms: () => 
    request.post<Api.Response>('/user/send-change-password-sms'),

  // 用户注册
  register: (data: Api.Auth.RegisterRequest) => 
    request.post<Api.Response<Api.Auth.UserInfo>>('/register', data),

  // 用户登录
  login: (data: Api.Auth.LoginRequest) => 
    request.post<Api.Response<Api.Auth.LoginResponse>>('/login', data),

  // 忘记密码
  forgotPassword: (data: Api.Auth.ForgotPasswordRequest) => 
    request.post<Api.Response<{ token: string }>>('/forgot-password', data),

  // 重置密码
  resetPassword: (data: Api.Auth.ResetPasswordRequest) => 
    request.post<Api.Response>('/reset-password', data),

  // 修改密码
  changePassword: (data: Api.Auth.ChangePasswordRequest) => 
    request.post<Api.Response>('/user/change-password', data),

  // 获取用户信息
  getUserProfile: () => 
    request.get<Api.Response<Api.Auth.UserInfo>>('/user/profile'),

  // 更新用户信息
  updateProfile: (data: { nickname: string }) =>
    request.put<Api.Response>('/user/profile', data)
};
```

### 5. Pinia状态管理

#### 5.1 用户认证Store
```typescript
// src/store/modules/auth/index.ts
import { defineStore } from 'pinia';
import { authApi } from '@/service/api';
import { localStg } from '@/utils/storage';
import { router } from '@/router';
import { $t } from '@/locales';

interface AuthState {
  token: string;
  userInfo: Api.Auth.UserInfo | null;
  loginLoading: boolean;
}

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    token: localStg.get('token') || '',
    userInfo: localStg.get('userInfo') || null,
    loginLoading: false
  }),

  getters: {
    // 是否已登录
    isLogin: state => Boolean(state.token),

    // 用户状态文本
    userStatusText: state => {
      if (!state.userInfo) return '';
      switch (state.userInfo.is_active) {
        case 0: return $t('auth.status.disabled');
        case 1: return $t('auth.status.active');
        case 2: return $t('auth.status.pending');
        default: return $t('auth.status.unknown');
      }
    },

    // 用户状态类型（用于UI显示）
    userStatusType: state => {
      if (!state.userInfo) return 'default';
      switch (state.userInfo.is_active) {
        case 0: return 'error';
        case 1: return 'success';
        case 2: return 'warning';
        default: return 'default';
      }
    },

    // 是否可以正常使用系统
    canUseSystem: state => state.userInfo?.is_active === 1,

    // 用户角色
    userRole: state => state.userInfo?.role || 'user',

    // 是否是管理员
    isAdmin: state => state.userInfo?.role === 'admin',

    // 是否是管理员或题库管理员
    isManager: state => ['admin', 'manager'].includes(state.userInfo?.role || '')
  },

  actions: {
    // 登录
    async login(loginForm: Api.Auth.LoginRequest) {
      this.loginLoading = true;
      try {
        const { data } = await authApi.login(loginForm);

        this.token = data.token;
        this.userInfo = data.user;

        // 存储到本地
        localStg.set('token', data.token);
        localStg.set('userInfo', data.user);

        // 检查用户状态
        if (data.user.is_active !== 1) {
          window.$message?.warning(this.userStatusText);
          if (data.user.is_active === 2) {
            window.$message?.info($t('auth.waitForApproval'));
          }
          return false;
        }

        window.$message?.success($t('auth.loginSuccess'));
        return true;
      } catch (error) {
        window.$message?.error($t('auth.loginFailed'));
        return false;
      } finally {
        this.loginLoading = false;
      }
    },

    // 注册
    async register(registerForm: Api.Auth.RegisterRequest) {
      try {
        await authApi.register(registerForm);
        window.$message?.success($t('auth.registerSuccess'));
        return true;
      } catch (error) {
        return false;
      }
    },

    // 登出
    logout() {
      this.token = '';
      this.userInfo = null;
      localStg.remove('token');
      localStg.remove('userInfo');
      router.push('/login');
      window.$message?.info($t('auth.logoutSuccess'));
    },

    // 获取用户信息
    async getUserInfo() {
      try {
        const data = await authApi.getUserProfile();
        this.userInfo = data;
        localStg.set('userInfo', data);
        return data;
      } catch (error) {
        this.logout();
        return null;
      }
    },

    // 更新用户信息
    async updateProfile(data: { nickname: string }) {
      try {
        await authApi.updateProfile(data);
        if (this.userInfo) {
          this.userInfo.nickname = data.nickname;
          localStg.set('userInfo', this.userInfo);
        }
        window.$message?.success($t('auth.updateSuccess'));
        return true;
      } catch (error) {
        return false;
      }
    }
  }
});
```

### 6. 路由守卫

#### 6.1 认证路由守卫
```typescript
// src/router/guard/auth.ts
import type { Router } from 'vue-router';
import { useAuthStore } from '@/store/modules/auth';
import { $t } from '@/locales';

export function createAuthGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {
    const authStore = useAuthStore();

    // 白名单路由（无需认证）
    const whiteList = ['/login', '/register', '/forgot-password', '/404', '/500'];

    if (whiteList.includes(to.path)) {
      next();
      return;
    }

    // 检查是否已登录
    if (!authStore.isLogin) {
      next('/login');
      return;
    }

    // 检查用户信息
    if (!authStore.userInfo) {
      try {
        await authStore.getUserInfo();
      } catch (error) {
        next('/login');
        return;
      }
    }

    // 检查用户状态
    if (!authStore.canUseSystem) {
      window.$message?.warning($t('auth.accountStatusError', { status: authStore.userStatusText }));
      authStore.logout();
      return;
    }

    // 检查角色权限
    if (to.meta?.requiresAuth && to.meta?.roles) {
      const userRole = authStore.userRole;
      const allowedRoles = to.meta.roles as string[];

      if (!allowedRoles.includes(userRole)) {
        window.$message?.error($t('auth.insufficientPermissions'));
        next('/403');
        return;
      }
    }

    next();
  });
}
```

### 7. 核心组件

#### 7.1 短信验证码组件
```vue
<!-- src/components/SmsCode/index.vue -->
<template>
  <div class="sms-code-container">
    <n-input-group>
      <n-input
        v-model:value="code"
        :placeholder="$t('auth.smsCodePlaceholder')"
        :maxlength="6"
        @input="handleInput"
      />
      <n-button
        :disabled="countdown > 0 || loading || !canSend"
        :loading="loading"
        @click="handleSendCode"
      >
        {{ countdown > 0 ? $t('auth.resendAfter', { seconds: countdown }) : $t('auth.getSmsCode') }}
      </n-button>
    </n-input-group>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { authApi } from '@/service/api';
import { $t } from '@/locales';

interface Props {
  modelValue: string;
  phone: string;
  type: 'register' | 'reset' | 'change';
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false
});

const emit = defineEmits<{
  'update:modelValue': [value: string];
}>();

const code = ref(props.modelValue);
const countdown = ref(0);
const loading = ref(false);

// 是否可以发送验证码
const canSend = computed(() => {
  return props.phone && props.phone.length === 11 && !props.disabled;
});

watch(() => props.modelValue, (val) => {
  code.value = val;
});

const handleInput = (value: string) => {
  emit('update:modelValue', value);
};

const handleSendCode = async () => {
  if (!canSend.value) {
    window.$message?.warning($t('auth.pleaseEnterPhone'));
    return;
  }

  loading.value = true;
  try {
    switch (props.type) {
      case 'register':
      case 'reset':
        await authApi.sendSms({ phone: props.phone, type: props.type });
        break;
      case 'change':
        await authApi.sendChangePasswordSms();
        break;
    }

    window.$message?.success($t('auth.smsCodeSent'));
    startCountdown();
  } catch (error) {
    // 错误已在拦截器中处理
  } finally {
    loading.value = false;
  }
};

const startCountdown = () => {
  countdown.value = 60;
  const timer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(timer);
    }
  }, 1000);
};
</script>
```

#### 7.2 用户状态标签组件
```vue
<!-- src/components/UserStatus/index.vue -->
<template>
  <n-tag :type="statusType" size="small">
    {{ statusText }}
  </n-tag>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { $t } from '@/locales';

interface Props {
  status: 0 | 1 | 2;
}

const props = defineProps<Props>();

const statusText = computed(() => {
  switch (props.status) {
    case 0: return $t('auth.status.disabled');
    case 1: return $t('auth.status.active');
    case 2: return $t('auth.status.pending');
    default: return $t('auth.status.unknown');
  }
});

const statusType = computed(() => {
  switch (props.status) {
    case 0: return 'error';
    case 1: return 'success';
    case 2: return 'warning';
    default: return 'default';
  }
});
</script>
```

### 8. 页面组件

#### 8.1 登录页面
```vue
<!-- src/views/login/index.vue -->
<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <h1 class="login-title">{{ $t('auth.login') }}</h1>
        <p class="login-subtitle">{{ $t('auth.loginSubtitle') }}</p>
      </div>

      <n-form ref="formRef" :model="loginForm" :rules="rules" size="large">
        <n-form-item path="phone">
          <n-input
            v-model:value="loginForm.phone"
            :placeholder="$t('auth.phonePlaceholder')"
            :maxlength="11"
            clearable
          >
            <template #prefix>
              <n-icon :component="PhoneOutlined" />
            </template>
          </n-input>
        </n-form-item>

        <n-form-item path="password">
          <n-input
            v-model:value="loginForm.password"
            type="password"
            :placeholder="$t('auth.passwordPlaceholder')"
            show-password-on="click"
            @keydown.enter="handleLogin"
          >
            <template #prefix>
              <n-icon :component="LockOutlined" />
            </template>
          </n-input>
        </n-form-item>

        <n-form-item>
          <n-button
            type="primary"
            block
            size="large"
            :loading="authStore.loginLoading"
            @click="handleLogin"
          >
            {{ $t('auth.login') }}
          </n-button>
        </n-form-item>

        <div class="login-links">
          <n-button text @click="$router.push('/register')">
            {{ $t('auth.register') }}
          </n-button>
          <n-button text @click="$router.push('/forgot-password')">
            {{ $t('auth.forgotPassword') }}
          </n-button>
        </div>
      </n-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { PhoneOutlined, LockOutlined } from '@vicons/antd';
import { useAuthStore } from '@/store/modules/auth';
import { $t } from '@/locales';

const router = useRouter();
const authStore = useAuthStore();

const formRef = ref();
const loginForm = reactive<Api.Auth.LoginRequest>({
  phone: '',
  password: ''
});

const rules = {
  phone: [
    { required: true, message: $t('auth.phoneRequired') },
    { pattern: /^1[3-9]\d{9}$/, message: $t('auth.phoneFormatError') }
  ],
  password: [
    { required: true, message: $t('auth.passwordRequired') },
    { min: 6, message: $t('auth.passwordMinLength') }
  ]
};

const handleLogin = async () => {
  try {
    await formRef.value?.validate();
    const success = await authStore.login(loginForm);
    if (success) {
      router.push('/dashboard');
    }
  } catch (error) {
    // 表单验证失败
  }
};
</script>

<style scoped>
.login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-card {
  width: 400px;
  padding: 40px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-title {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.login-subtitle {
  color: #666;
  font-size: 14px;
}

.login-links {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
}
</style>
```

#### 8.2 注册页面
```vue
<!-- src/views/register/index.vue -->
<template>
  <div class="register-container">
    <div class="register-card">
      <div class="register-header">
        <h1 class="register-title">{{ $t('auth.register') }}</h1>
        <p class="register-subtitle">{{ $t('auth.registerSubtitle') }}</p>
      </div>

      <n-form ref="formRef" :model="registerForm" :rules="rules" size="large">
        <n-form-item path="phone">
          <n-input
            v-model:value="registerForm.phone"
            :placeholder="$t('auth.phonePlaceholder')"
            :maxlength="11"
            clearable
          >
            <template #prefix>
              <n-icon :component="PhoneOutlined" />
            </template>
          </n-input>
        </n-form-item>

        <n-form-item path="nickname">
          <n-input
            v-model:value="registerForm.nickname"
            :placeholder="$t('auth.nicknamePlaceholder')"
            :maxlength="20"
            clearable
          >
            <template #prefix>
              <n-icon :component="UserOutlined" />
            </template>
          </n-input>
        </n-form-item>

        <n-form-item path="password">
          <n-input
            v-model:value="registerForm.password"
            type="password"
            :placeholder="$t('auth.passwordPlaceholder')"
            show-password-on="click"
          >
            <template #prefix>
              <n-icon :component="LockOutlined" />
            </template>
          </n-input>
        </n-form-item>

        <n-form-item path="confirmPassword">
          <n-input
            v-model:value="confirmPassword"
            type="password"
            :placeholder="$t('auth.confirmPasswordPlaceholder')"
            show-password-on="click"
          >
            <template #prefix>
              <n-icon :component="LockOutlined" />
            </template>
          </n-input>
        </n-form-item>

        <n-form-item path="sms_code">
          <SmsCode
            v-model="registerForm.sms_code"
            :phone="registerForm.phone"
            type="register"
          />
        </n-form-item>

        <n-form-item>
          <n-button
            type="primary"
            block
            size="large"
            :loading="loading"
            @click="handleRegister"
          >
            {{ $t('auth.register') }}
          </n-button>
        </n-form-item>

        <div class="register-links">
          <n-button text @click="$router.push('/login')">
            {{ $t('auth.backToLogin') }}
          </n-button>
        </div>
      </n-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { PhoneOutlined, LockOutlined, UserOutlined } from '@vicons/antd';
import { useAuthStore } from '@/store/modules/auth';
import SmsCode from '@/components/SmsCode/index.vue';
import { $t } from '@/locales';

const router = useRouter();
const authStore = useAuthStore();

const formRef = ref();
const loading = ref(false);
const confirmPassword = ref('');

const registerForm = reactive<Api.Auth.RegisterRequest>({
  phone: '',
  password: '',
  nickname: '',
  sms_code: ''
});

const rules = {
  phone: [
    { required: true, message: $t('auth.phoneRequired') },
    { pattern: /^1[3-9]\d{9}$/, message: $t('auth.phoneFormatError') }
  ],
  nickname: [
    { required: true, message: $t('auth.nicknameRequired') },
    { min: 2, max: 20, message: $t('auth.nicknameLength') }
  ],
  password: [
    { required: true, message: $t('auth.passwordRequired') },
    { min: 6, message: $t('auth.passwordMinLength') }
  ],
  confirmPassword: [
    { required: true, message: $t('auth.confirmPasswordRequired') },
    {
      validator: (rule: any, value: string) => {
        return value === registerForm.password;
      },
      message: $t('auth.passwordNotMatch')
    }
  ],
  sms_code: [
    { required: true, message: $t('auth.smsCodeRequired') },
    { len: 6, message: $t('auth.smsCodeLength') }
  ]
};

const handleRegister = async () => {
  try {
    await formRef.value?.validate();
    loading.value = true;

    const success = await authStore.register(registerForm);
    if (success) {
      router.push('/login');
    }
  } catch (error) {
    // 表单验证失败
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.register-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.register-card {
  width: 400px;
  padding: 40px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.register-header {
  text-align: center;
  margin-bottom: 32px;
}

.register-title {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.register-subtitle {
  color: #666;
  font-size: 14px;
}

.register-links {
  text-align: center;
  margin-top: 16px;
}
</style>
```

## 📅 开发计划

### 阶段一：基础环境搭建（1-2天）

#### Day 1: 项目初始化
- [ ] 创建Soybean Admin项目
- [ ] 配置开发环境和构建工具
- [ ] 设置代码规范（ESLint、Prettier、Stylelint）
- [ ] 配置Git提交规范（Husky、Commitlint）
- [ ] 设置环境变量配置

#### Day 2: 基础配置
- [ ] 配置HTTP请求拦截器
- [ ] 设置路由结构和守卫
- [ ] 配置国际化（i18n）
- [ ] 设置主题和样式系统
- [ ] 配置状态管理（Pinia）

### 阶段二：认证系统开发（3-5天）

#### Day 3: 类型定义和API封装
- [ ] 定义TypeScript类型
- [ ] 封装认证相关API
- [ ] 创建用户状态Store
- [ ] 实现本地存储工具

#### Day 4: 核心组件开发
- [ ] 开发短信验证码组件
- [ ] 开发用户状态标签组件
- [ ] 开发密码强度指示器
- [ ] 开发表单验证工具

#### Day 5: 页面开发
- [ ] 开发登录页面
- [ ] 开发注册页面
- [ ] 开发忘记密码页面
- [ ] 开发修改密码页面

### 阶段三：功能完善（2-3天）

#### Day 6: 高级功能
- [ ] 实现路由权限控制
- [ ] 开发用户信息管理
- [ ] 实现错误处理机制
- [ ] 添加加载状态管理

#### Day 7: 用户体验优化
- [ ] 添加动画效果
- [ ] 优化响应式设计
- [ ] 实现暗色主题支持
- [ ] 添加无障碍访问支持

### 阶段四：测试和优化（2-3天）

#### Day 8: 测试
- [ ] 单元测试编写
- [ ] 集成测试
- [ ] E2E测试
- [ ] 性能测试

#### Day 9: 优化和部署
- [ ] 代码优化和重构
- [ ] 构建优化
- [ ] 部署配置
- [ ] 文档完善

## 🛠️ 工具函数

### 1. 表单验证工具
```typescript
// src/utils/validation.ts
import { $t } from '@/locales';

// 手机号验证
export const phoneValidator = (phone: string): boolean => {
  return /^1[3-9]\d{9}$/.test(phone);
};

// 密码强度验证
export const passwordValidator = {
  // 最小长度验证
  minLength: (password: string, min = 6): boolean => {
    return password.length >= min;
  },

  // 包含数字
  hasNumber: (password: string): boolean => {
    return /\d/.test(password);
  },

  // 包含字母
  hasLetter: (password: string): boolean => {
    return /[a-zA-Z]/.test(password);
  },

  // 包含特殊字符
  hasSpecialChar: (password: string): boolean => {
    return /[^a-zA-Z0-9]/.test(password);
  },

  // 获取密码强度
  getStrength: (password: string): 'weak' | 'medium' | 'strong' => {
    let score = 0;
    if (password.length >= 8) score++;
    if (/\d/.test(password)) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[^a-zA-Z0-9]/.test(password)) score++;

    if (score <= 2) return 'weak';
    if (score <= 3) return 'medium';
    return 'strong';
  }
};

// 表单验证规则
export const validationRules = {
  phone: [
    { required: true, message: $t('validation.phoneRequired') },
    { pattern: /^1[3-9]\d{9}$/, message: $t('validation.phoneFormat') }
  ],

  password: [
    { required: true, message: $t('validation.passwordRequired') },
    { min: 6, message: $t('validation.passwordMinLength') }
  ],

  nickname: [
    { required: true, message: $t('validation.nicknameRequired') },
    { min: 2, max: 20, message: $t('validation.nicknameLength') }
  ],

  smsCode: [
    { required: true, message: $t('validation.smsCodeRequired') },
    { len: 6, message: $t('validation.smsCodeLength') }
  ]
};
```

### 2. 用户状态工具
```typescript
// src/utils/user.ts
import { $t } from '@/locales';

// 用户状态工具
export const userStatusUtils = {
  // 获取状态文本
  getStatusText(status: 0 | 1 | 2): string {
    switch (status) {
      case 0: return $t('auth.status.disabled');
      case 1: return $t('auth.status.active');
      case 2: return $t('auth.status.pending');
      default: return $t('auth.status.unknown');
    }
  },

  // 获取状态标签类型
  getStatusType(status: 0 | 1 | 2): 'error' | 'success' | 'warning' | 'default' {
    switch (status) {
      case 0: return 'error';
      case 1: return 'success';
      case 2: return 'warning';
      default: return 'default';
    }
  },

  // 检查是否可以登录
  canLogin(status: 0 | 1 | 2): boolean {
    return status === 1;
  },

  // 检查是否是管理员
  isAdmin(role: string): boolean {
    return role === 'admin';
  },

  // 检查是否是管理员或题库管理员
  isManager(role: string): boolean {
    return ['admin', 'manager'].includes(role);
  }
};

// 权限检查工具
export const permissionUtils = {
  // 检查用户权限
  hasPermission(userRole: string, requiredRoles: string[]): boolean {
    return requiredRoles.includes(userRole);
  },

  // 检查是否有管理员权限
  hasAdminPermission(userRole: string): boolean {
    return userRole === 'admin';
  },

  // 检查是否有管理权限（管理员或题库管理员）
  hasManagerPermission(userRole: string): boolean {
    return ['admin', 'manager'].includes(userRole);
  }
};
```

### 3. 时间格式化工具
```typescript
// src/utils/time.ts
import { format, formatDistanceToNow } from 'date-fns';
import { zhCN, enUS } from 'date-fns/locale';
import { useAppStore } from '@/store/modules/app';

// 获取当前语言环境
const getLocale = () => {
  const appStore = useAppStore();
  return appStore.locale === 'zh-CN' ? zhCN : enUS;
};

// 格式化日期时间
export const formatDateTime = (date: string | Date, pattern = 'yyyy-MM-dd HH:mm:ss'): string => {
  return format(new Date(date), pattern, { locale: getLocale() });
};

// 格式化相对时间
export const formatRelativeTime = (date: string | Date): string => {
  return formatDistanceToNow(new Date(date), {
    addSuffix: true,
    locale: getLocale()
  });
};

// 格式化冻结剩余时间
export const formatFreezeTime = (freezeUntil: string): string => {
  const now = new Date();
  const freezeTime = new Date(freezeUntil);

  if (freezeTime <= now) {
    return '';
  }

  const diff = freezeTime.getTime() - now.getTime();
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`;
  }
  return `${minutes}分钟`;
};
```

### 4. 错误处理工具
```typescript
// src/utils/error.ts
import { $t } from '@/locales';

// 错误类型定义
export interface ApiError {
  code: number;
  message: string;
  data?: any;
}

// 错误处理工具
export const errorUtils = {
  // 处理API错误
  handleApiError(error: ApiError): string {
    switch (error.code) {
      case 400:
        return error.message || $t('error.badRequest');
      case 401:
        return $t('error.unauthorized');
      case 403:
        return $t('error.forbidden');
      case 404:
        return $t('error.notFound');
      case 500:
        return $t('error.serverError');
      default:
        return error.message || $t('error.unknown');
    }
  },

  // 处理网络错误
  handleNetworkError(error: any): string {
    if (error.code === 'NETWORK_ERROR') {
      return $t('error.networkError');
    }
    if (error.code === 'TIMEOUT') {
      return $t('error.timeout');
    }
    return $t('error.requestFailed');
  },

  // 显示错误消息
  showError(error: any): void {
    let message = '';

    if (error.response?.data) {
      message = this.handleApiError(error.response.data);
    } else {
      message = this.handleNetworkError(error);
    }

    window.$message?.error(message);
  }
};
```

## 🌍 国际化配置

### 1. 中文语言包
```typescript
// src/locales/langs/zh-cn.ts
export default {
  auth: {
    login: '登录',
    register: '注册',
    logout: '退出登录',
    forgotPassword: '忘记密码',
    changePassword: '修改密码',
    loginSubtitle: '欢迎回来，请登录您的账户',
    registerSubtitle: '创建新账户，开始您的旅程',

    // 表单字段
    phonePlaceholder: '请输入手机号',
    passwordPlaceholder: '请输入密码',
    confirmPasswordPlaceholder: '请确认密码',
    nicknamePlaceholder: '请输入昵称',
    smsCodePlaceholder: '请输入验证码',

    // 验证消息
    phoneRequired: '请输入手机号',
    phoneFormatError: '手机号格式不正确',
    passwordRequired: '请输入密码',
    passwordMinLength: '密码至少6位',
    confirmPasswordRequired: '请确认密码',
    passwordNotMatch: '两次密码输入不一致',
    nicknameRequired: '请输入昵称',
    nicknameLength: '昵称长度为2-20个字符',
    smsCodeRequired: '请输入验证码',
    smsCodeLength: '验证码为6位数字',

    // 状态
    status: {
      active: '正常',
      disabled: '已禁用',
      pending: '审核中',
      unknown: '未知状态'
    },

    // 消息
    loginSuccess: '登录成功',
    loginFailed: '登录失败',
    registerSuccess: '注册成功，请等待管理员审核',
    logoutSuccess: '退出登录成功',
    updateSuccess: '更新成功',
    smsCodeSent: '验证码发送成功',
    getSmsCode: '获取验证码',
    resendAfter: '{seconds}秒后重发',
    pleaseEnterPhone: '请先输入手机号',
    waitForApproval: '请等待管理员审核',
    accountStatusError: '账户状态异常：{status}',
    insufficientPermissions: '权限不足',
    backToLogin: '返回登录'
  },

  validation: {
    phoneRequired: '请输入手机号',
    phoneFormat: '手机号格式不正确',
    passwordRequired: '请输入密码',
    passwordMinLength: '密码至少6位',
    nicknameRequired: '请输入昵称',
    nicknameLength: '昵称长度为2-20个字符',
    smsCodeRequired: '请输入验证码',
    smsCodeLength: '验证码为6位数字'
  },

  error: {
    badRequest: '请求参数错误',
    unauthorized: '未授权，请重新登录',
    forbidden: '权限不足',
    notFound: '资源不存在',
    serverError: '服务器内部错误',
    networkError: '网络连接失败',
    timeout: '请求超时',
    requestFailed: '请求失败',
    unknown: '未知错误'
  },

  common: {
    confirm: '确认',
    cancel: '取消',
    save: '保存',
    delete: '删除',
    edit: '编辑',
    view: '查看',
    search: '搜索',
    reset: '重置',
    submit: '提交',
    loading: '加载中...',
    noData: '暂无数据',
    requestFailed: '请求失败'
  }
};
```

### 2. 英文语言包
```typescript
// src/locales/langs/en-us.ts
export default {
  auth: {
    login: 'Login',
    register: 'Register',
    logout: 'Logout',
    forgotPassword: 'Forgot Password',
    changePassword: 'Change Password',
    loginSubtitle: 'Welcome back, please login to your account',
    registerSubtitle: 'Create a new account to start your journey',

    // Form fields
    phonePlaceholder: 'Please enter phone number',
    passwordPlaceholder: 'Please enter password',
    confirmPasswordPlaceholder: 'Please confirm password',
    nicknamePlaceholder: 'Please enter nickname',
    smsCodePlaceholder: 'Please enter verification code',

    // Validation messages
    phoneRequired: 'Please enter phone number',
    phoneFormatError: 'Invalid phone number format',
    passwordRequired: 'Please enter password',
    passwordMinLength: 'Password must be at least 6 characters',
    confirmPasswordRequired: 'Please confirm password',
    passwordNotMatch: 'Passwords do not match',
    nicknameRequired: 'Please enter nickname',
    nicknameLength: 'Nickname must be 2-20 characters',
    smsCodeRequired: 'Please enter verification code',
    smsCodeLength: 'Verification code must be 6 digits',

    // Status
    status: {
      active: 'Active',
      disabled: 'Disabled',
      pending: 'Pending',
      unknown: 'Unknown'
    },

    // Messages
    loginSuccess: 'Login successful',
    loginFailed: 'Login failed',
    registerSuccess: 'Registration successful, please wait for admin approval',
    logoutSuccess: 'Logout successful',
    updateSuccess: 'Update successful',
    smsCodeSent: 'Verification code sent successfully',
    getSmsCode: 'Get Code',
    resendAfter: 'Resend after {seconds}s',
    pleaseEnterPhone: 'Please enter phone number first',
    waitForApproval: 'Please wait for admin approval',
    accountStatusError: 'Account status error: {status}',
    insufficientPermissions: 'Insufficient permissions',
    backToLogin: 'Back to Login'
  }
  // ... 其他翻译
};
```

## 📋 开发检查清单

### 环境搭建
- [ ] Node.js 18+ 安装
- [ ] pnpm 包管理器安装
- [ ] VS Code 编辑器配置
- [ ] 必要的VS Code插件安装
- [ ] Git 配置

### 项目配置
- [ ] Soybean Admin 项目创建
- [ ] 环境变量配置
- [ ] 代码规范配置
- [ ] 构建配置优化
- [ ] 开发服务器配置

### 功能开发
- [ ] TypeScript 类型定义
- [ ] API 服务封装
- [ ] 状态管理实现
- [ ] 路由配置和守卫
- [ ] 核心组件开发
- [ ] 页面组件开发
- [ ] 工具函数实现
- [ ] 国际化配置

### 测试和优化
- [ ] 单元测试编写
- [ ] 集成测试
- [ ] 性能优化
- [ ] 代码审查
- [ ] 文档完善

## 🎯 开发建议

### 1. 代码规范
- 使用 TypeScript 严格模式
- 遵循 Vue 3 Composition API 最佳实践
- 使用 ESLint + Prettier 保持代码风格一致
- 组件命名使用 PascalCase
- 文件命名使用 kebab-case

### 2. 性能优化
- 使用 `defineAsyncComponent` 实现路由懒加载
- 合理使用 `computed` 和 `watch`
- 避免在模板中使用复杂计算
- 使用 `v-memo` 优化列表渲染
- 图片资源使用 WebP 格式

### 3. 用户体验
- 添加适当的加载状态
- 实现骨架屏效果
- 提供友好的错误提示
- 支持键盘导航
- 实现响应式设计

### 4. 安全考虑
- 输入数据验证和清理
- XSS 攻击防护
- CSRF 攻击防护
- 敏感信息加密存储
- 定期更新依赖包

## 📞 技术支持

### 开发资源
- **Soybean Admin 文档**: [https://docs.soybeanjs.cn](https://docs.soybeanjs.cn)
- **Vue 3 文档**: [https://vuejs.org](https://vuejs.org)
- **Naive UI 文档**: [https://www.naiveui.com](https://www.naiveui.com)
- **TypeScript 文档**: [https://www.typescriptlang.org](https://www.typescriptlang.org)

### 问题反馈
- 项目仓库: [GitHub链接]
- 技术文档: [文档链接]
- 问题反馈: [Issue链接]
- 技术交流群: [群链接]

---

**文档版本**: v2.0
**最后更新**: 2025-06-19 21:59:38 CST
**维护者**: 开发团队
```
