<template>
  <div class="sms-code-container">
    <n-input-group>
      <n-input
        v-model:value="code"
        :placeholder="$t('auth.smsCodePlaceholder')"
        :maxlength="6"
        @input="handleInput"
      />
      <n-button
        :disabled="countdown > 0 || loading || !canSend"
        :loading="loading"
        @click="handleSendCode"
      >
        {{ countdown > 0 ? $t('auth.resendAfter', { seconds: countdown }) : $t('auth.getSmsCode') }}
      </n-button>
    </n-input-group>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { sendSms, sendChangePasswordSms } from '@/service/api';
import { $t } from '@/locales';

interface Props {
  modelValue: string;
  phone: string;
  type: 'register' | 'reset' | 'change';
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false
});

const emit = defineEmits<{
  'update:modelValue': [value: string];
}>();

const code = ref(props.modelValue);
const countdown = ref(0);
const loading = ref(false);

// 是否可以发送验证码
const canSend = computed(() => {
  return props.phone && props.phone.length === 11 && !props.disabled;
});

watch(() => props.modelValue, (val) => {
  code.value = val;
});

const handleInput = (value: string) => {
  emit('update:modelValue', value);
};

const handleSendCode = async () => {
  if (!canSend.value) {
    window.$message?.warning($t('auth.pleaseEnterPhone'));
    return;
  }

  loading.value = true;
  try {
    switch (props.type) {
      case 'register':
      case 'reset':
        await sendSms({ phone: props.phone, type: props.type });
        break;
      case 'change':
        await sendChangePasswordSms();
        break;
    }

    window.$message?.success($t('auth.smsCodeSent'));
    startCountdown();
  } catch (error) {
    // 错误已在拦截器中处理
  } finally {
    loading.value = false;
  }
};

const startCountdown = () => {
  countdown.value = 60;
  const timer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(timer);
    }
  }, 1000);
};
</script>

<style scoped>
.sms-code-container {
  width: 100%;
}
</style>
