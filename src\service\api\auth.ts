import { request, authRequest } from '../request';

/**
 * <PERSON><PERSON> (原有的登录方式，保持兼容)
 *
 * @param userName User name
 * @param password Password
 */
export function fetchLogin(userName: string, password: string) {
  return request<Api.Auth.LoginToken>({
    url: '/auth/login',
    method: 'post',
    data: {
      userName,
      password
    }
  });
}

/** Get user info (原有的获取用户信息，保持兼容) */
export function fetchGetUserInfo() {
  return request<Api.Auth.UserInfo>({ url: '/auth/getUserInfo' });
}

/**
 * Refresh token (原有的刷新token，保持兼容)
 *
 * @param refreshToken Refresh token
 */
export function fetchRefreshToken(refreshToken: string) {
  return request<Api.Auth.LoginToken>({
    url: '/auth/refreshToken',
    method: 'post',
    data: {
      refreshToken
    }
  });
}

// 新的认证API，按照qianduan.md文档规范实现

/**
 * 发送短信验证码
 */
export function sendSms(data: Api.Auth.SmsRequest) {
  return authRequest<Api.Response<{ code?: string }>>({
    url: '/send-sms',
    method: 'post',
    data
  });
}

/**
 * 发送修改密码验证码（需JWT认证）
 */
export function sendChangePasswordSms() {
  return authRequest<Api.Response>({
    url: '/user/send-change-password-sms',
    method: 'post'
  });
}

/**
 * 用户注册
 */
export function register(data: Api.Auth.RegisterRequest) {
  return authRequest<Api.Response<Api.Auth.RegisterResponse>>({
    url: '/register',
    method: 'post',
    data
  });
}

/**
 * 用户登录（新的手机号登录方式）
 */
export function login(data: Api.Auth.LoginRequest) {
  return authRequest<Api.Response<Api.Auth.LoginResponse>>({
    url: '/login',
    method: 'post',
    data
  });
}

/**
 * 忘记密码 - 第一步：获取重置令牌
 */
export function forgotPassword(data: Api.Auth.ForgotPasswordRequest) {
  return authRequest<Api.Response<Api.Auth.ForgotPasswordResponse>>({
    url: '/forgot-password',
    method: 'post',
    data
  });
}

/**
 * 忘记密码 - 第二步：重置密码
 */
export function resetPassword(data: Api.Auth.ResetPasswordRequest) {
  return authRequest<Api.Response>({
    url: '/reset-password',
    method: 'post',
    data
  });
}

/**
 * 修改密码（需JWT认证）
 */
export function changePassword(data: Api.Auth.ChangePasswordRequest) {
  return authRequest<Api.Response>({
    url: '/user/change-password',
    method: 'post',
    data
  });
}

/**
 * 获取用户信息（需JWT认证）
 */
export function getUserProfile() {
  return authRequest<Api.Response<Api.Auth.UserProfile>>({
    url: '/user/profile',
    method: 'get'
  });
}

/**
 * 更新用户信息（需JWT认证）
 */
export function updateProfile(data: Api.Auth.UpdateProfileRequest) {
  return authRequest<Api.Response>({
    url: '/user/profile',
    method: 'put',
    data
  });
}

/**
 * return custom backend error
 *
 * @param code error code
 * @param msg error message
 */
export function fetchCustomBackendError(code: string, msg: string) {
  return request({ url: '/auth/error', params: { code, msg } });
}
