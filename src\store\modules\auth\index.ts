import { computed, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { defineStore } from 'pinia';
import { useLoading } from '@sa/hooks';
import { fetchGetUserInfo, fetchLogin, login as newLogin, register, getUserProfile } from '@/service/api';
import { useRouterPush } from '@/hooks/common/router';
import { localStg } from '@/utils/storage';
import { SetupStoreId } from '@/enum';
import { $t } from '@/locales';
import { useRouteStore } from '../route';
import { useTabStore } from '../tab';
import { clearAuthStorage, getToken } from './shared';

export const useAuthStore = defineStore(SetupStoreId.Auth, () => {
  const route = useRoute();
  const authStore = useAuthStore();
  const routeStore = useRouteStore();
  const tabStore = useTabStore();
  const { toLogin, redirectFromLogin } = useRouterPush(false);
  const { loading: loginLoading, startLoading, endLoading } = useLoading();

  const token = ref(getToken());

  const userInfo: Api.Auth.UserInfo = reactive({
    userId: '',
    userName: '',
    roles: [],
    buttons: []
  });

  // 新的用户信息状态，用于新的认证系统
  const newUserInfo = ref<Api.Auth.NewUserInfo | null>(null);

  /** is super role in static route */
  const isStaticSuper = computed(() => {
    const { VITE_AUTH_ROUTE_MODE, VITE_STATIC_SUPER_ROLE } = import.meta.env;

    return VITE_AUTH_ROUTE_MODE === 'static' && userInfo.roles.includes(VITE_STATIC_SUPER_ROLE);
  });

  /** Is login */
  const isLogin = computed(() => Boolean(token.value));

  // 新的计算属性，用于新的用户系统
  /** 用户状态文本 */
  const userStatusText = computed(() => {
    if (!newUserInfo.value) return '';
    switch (newUserInfo.value.is_active) {
      case 0: return $t('auth.status.disabled');
      case 1: return $t('auth.status.active');
      case 2: return $t('auth.status.pending');
      default: return $t('auth.status.unknown');
    }
  });

  /** 用户状态类型（用于UI显示） */
  const userStatusType = computed(() => {
    if (!newUserInfo.value) return 'default';
    switch (newUserInfo.value.is_active) {
      case 0: return 'error';
      case 1: return 'success';
      case 2: return 'warning';
      default: return 'default';
    }
  });

  /** 是否可以正常使用系统 */
  const canUseSystem = computed(() => newUserInfo.value?.is_active === 1);

  /** 用户角色 */
  const userRole = computed(() => newUserInfo.value?.role || 'user');

  /** 是否是管理员 */
  const isAdmin = computed(() => newUserInfo.value?.role === 'admin');

  /** 是否是管理员或题库管理员 */
  const isManager = computed(() => ['admin', 'manager'].includes(newUserInfo.value?.role || ''));

  /** Reset auth store */
  async function resetStore() {
    recordUserId();

    clearAuthStorage();

    authStore.$reset();

    if (!route.meta.constant) {
      await toLogin();
    }

    tabStore.cacheTabs();
    routeStore.resetStore();
  }

  /** Record the user ID of the previous login session Used to compare with the current user ID on next login */
  function recordUserId() {
    if (!userInfo.userId) {
      return;
    }

    // Store current user ID locally for next login comparison
    localStg.set('lastLoginUserId', userInfo.userId);
  }

  /**
   * Check if current login user is different from previous login user If different, clear all tabs
   *
   * @returns {boolean} Whether to clear all tabs
   */
  function checkTabClear(): boolean {
    if (!userInfo.userId) {
      return false;
    }

    const lastLoginUserId = localStg.get('lastLoginUserId');

    // Clear all tabs if current user is different from previous user
    if (!lastLoginUserId || lastLoginUserId !== userInfo.userId) {
      localStg.remove('globalTabs');
      tabStore.clearTabs();

      localStg.remove('lastLoginUserId');
      return true;
    }

    localStg.remove('lastLoginUserId');
    return false;
  }

  /**
   * Login
   *
   * @param userName User name
   * @param password Password
   * @param [redirect=true] Whether to redirect after login. Default is `true`
   */
  async function login(userName: string, password: string, redirect = true) {
    startLoading();

    const { data: loginToken, error } = await fetchLogin(userName, password);

    if (!error) {
      const pass = await loginByToken(loginToken);

      if (pass) {
        // Check if the tab needs to be cleared
        const isClear = checkTabClear();
        let needRedirect = redirect;

        if (isClear) {
          // If the tab needs to be cleared,it means we don't need to redirect.
          needRedirect = false;
        }
        await redirectFromLogin(needRedirect);

        window.$notification?.success({
          title: $t('page.login.common.loginSuccess'),
          content: $t('page.login.common.welcomeBack', { userName: userInfo.userName }),
          duration: 4500
        });
      }
    } else {
      resetStore();
    }

    endLoading();
  }

  async function loginByToken(loginToken: Api.Auth.LoginToken) {
    // 1. stored in the localStorage, the later requests need it in headers
    localStg.set('token', loginToken.token);
    localStg.set('refreshToken', loginToken.refreshToken);

    // 2. get user info
    const pass = await getUserInfo();

    if (pass) {
      token.value = loginToken.token;

      return true;
    }

    return false;
  }

  async function getUserInfo() {
    const { data: info, error } = await fetchGetUserInfo();

    if (!error) {
      // update store
      Object.assign(userInfo, info);

      return true;
    }

    return false;
  }

  async function initUserInfo() {
    const hasToken = getToken();

    if (hasToken) {
      const pass = await getUserInfo();

      if (!pass) {
        resetStore();
      }
    }
  }

  // 新的认证方法

  /** 新的手机号登录方法 */
  async function phoneLogin(loginForm: Api.Auth.LoginRequest, redirect = true) {
    startLoading();

    try {
      const { data } = await newLogin(loginForm);

      // 存储token
      localStg.set('token', data.token);
      localStg.set('userInfo', data.user);

      token.value = data.token;
      newUserInfo.value = data.user;

      // 检查用户状态
      if (data.user.is_active !== 1) {
        window.$message?.warning(userStatusText.value);
        if (data.user.is_active === 2) {
          window.$message?.info($t('auth.waitForApproval'));
        }
        endLoading();
        return false;
      }

      if (redirect) {
        await redirectFromLogin(true);
      }

      window.$message?.success($t('auth.loginSuccess'));
      endLoading();
      return true;
    } catch (error) {
      endLoading();
      return false;
    }
  }

  /** 用户注册方法 */
  async function userRegister(registerForm: Api.Auth.RegisterRequest) {
    try {
      await register(registerForm);
      window.$message?.success($t('auth.registerSuccess'));
      return true;
    } catch (error) {
      return false;
    }
  }

  /** 获取新的用户信息 */
  async function getNewUserInfo() {
    try {
      const data = await getUserProfile();
      newUserInfo.value = data;
      localStg.set('userInfo', data);
      return data;
    } catch (error) {
      resetStore();
      return null;
    }
  }

  /** 登出方法 */
  function logout() {
    token.value = '';
    newUserInfo.value = null;
    localStg.remove('token');
    localStg.remove('userInfo');
    window.$message?.info($t('auth.logoutSuccess'));
  }

  return {
    token,
    userInfo,
    newUserInfo,
    isStaticSuper,
    isLogin,
    loginLoading,
    userStatusText,
    userStatusType,
    canUseSystem,
    userRole,
    isAdmin,
    isManager,
    resetStore,
    login,
    phoneLogin,
    userRegister,
    getNewUserInfo,
    logout,
    initUserInfo
  };
});
