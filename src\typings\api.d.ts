/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
  namespace Common {
    /** common params of paginating */
    interface PaginatingCommonParams {
      /** current page number */
      current: number;
      /** page size */
      size: number;
      /** total count */
      total: number;
    }

    /** common params of paginating query list data */
    interface PaginatingQueryRecord<T = any> extends PaginatingCommonParams {
      records: T[];
    }

    /** common search params of table */
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'current' | 'size'>;

    /**
     * enable status
     *
     * - "1": enabled
     * - "2": disabled
     */
    type EnableStatus = '1' | '2';

    /** common record */
    type CommonRecord<T = any> = {
      /** record id */
      id: number;
      /** record creator */
      createBy: string;
      /** record create time */
      createTime: string;
      /** record updater */
      updateBy: string;
      /** record update time */
      updateTime: string;
      /** record status */
      status: EnableStatus | null;
    } & T;
  }

  // 统一响应格式
  interface Response<T = any> {
    code: number;
    message: string;
    data: T;
  }

  /**
   * namespace Auth
   *
   * backend api module: "auth"
   */
  namespace Auth {
    interface LoginToken {
      token: string;
      refreshToken: string;
    }

    interface UserInfo {
      userId: string;
      userName: string;
      roles: string[];
      buttons: string[];
    }

    // 根据qianduan.md文档定义的新用户信息结构
    interface NewUserInfo {
      id: number;
      phone: string;
      nickname: string;
      role: 'admin' | 'manager' | 'user';
      balance: number;
      is_active: 0 | 1 | 2;
      created_at: string;
    }

    // 短信验证码请求
    interface SmsRequest {
      phone: string;
      type: 'register' | 'reset';
    }

    // 登录请求
    interface LoginRequest {
      phone: string;
      password: string;
    }

    // 登录响应
    interface LoginResponse {
      token: string;
      user: NewUserInfo;
    }

    // 注册请求
    interface RegisterRequest {
      phone: string;
      password: string;
      nickname: string;
      sms_code: string;
    }

    // 注册响应
    interface RegisterResponse {
      id: number;
      phone: string;
      nickname: string;
      is_active: 0 | 1 | 2;
      status: string;
    }

    // 忘记密码请求
    interface ForgotPasswordRequest {
      phone: string;
      sms_code: string;
    }

    // 忘记密码响应
    interface ForgotPasswordResponse {
      token: string;
    }

    // 重置密码请求
    interface ResetPasswordRequest {
      token: string;
      new_password: string;
      sms_code: string;
    }

    // 修改密码请求
    interface ChangePasswordRequest {
      old_password: string;
      new_password: string;
      sms_code: string;
    }

    // 用户资料
    interface UserProfile {
      id: number;
      phone: string;
      nickname: string;
      role: string;
      balance: number;
      is_active: 0 | 1 | 2;
      created_at: string;
    }

    // 更新用户信息请求
    interface UpdateProfileRequest {
      nickname: string;
    }
  }

  // 统一响应格式
  interface Response<T = any> {
    code: number;
    message: string;
    data: T;
  }

  /**
   * namespace Route
   *
   * backend api module: "route"
   */
  namespace Route {
    type ElegantConstRoute = import('@elegant-router/types').ElegantConstRoute;

    interface MenuRoute extends ElegantConstRoute {
      id: string;
    }

    interface UserRoute {
      routes: MenuRoute[];
      home: import('@elegant-router/types').LastLevelRouteKey;
    }
  }
}
