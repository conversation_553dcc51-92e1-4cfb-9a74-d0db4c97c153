<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import { useRouterPush } from '@/hooks/common/router';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useAuthStore } from '@/store/modules/auth';
import { $t } from '@/locales';
import SmsCode from '@/components/common/sms-code.vue';

defineOptions({
  name: 'Register'
});

const { toggleLoginModule } = useRouterPush();
const { formRef, validate } = useNaiveForm();
const authStore = useAuthStore();

interface FormModel {
  phone: string;
  nickname: string;
  password: string;
  confirmPassword: string;
  sms_code: string;
}

const model: FormModel = reactive({
  phone: '',
  nickname: '',
  password: '',
  confirmPassword: '',
  sms_code: ''
});

const loading = ref(false);

const rules = computed<Record<keyof FormModel, App.Global.FormRule[]>>(() => {
  const { formRules, createConfirmPwdRule } = useFormRules();

  return {
    phone: formRules.phone,
    nickname: [
      { required: true, message: $t('auth.nicknameRequired') },
      { min: 2, max: 20, message: $t('auth.nicknameLength') }
    ],
    password: formRules.pwd,
    confirmPassword: createConfirmPwdRule(model.password),
    sms_code: [
      { required: true, message: $t('auth.smsCodeRequired') },
      { len: 6, message: $t('auth.smsCodeLength') }
    ]
  };
});

async function handleSubmit() {
  try {
    await validate();
    loading.value = true;

    const success = await authStore.userRegister({
      phone: model.phone,
      nickname: model.nickname,
      password: model.password,
      sms_code: model.sms_code
    });

    if (success) {
      toggleLoginModule('pwd-login');
    }
  } catch (error) {
    // 表单验证失败
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules" size="large" :show-label="false" @keyup.enter="handleSubmit">
    <NFormItem path="phone">
      <NInput v-model:value="model.phone" :placeholder="$t('page.login.common.phonePlaceholder')" />
    </NFormItem>

    <NFormItem path="nickname">
      <NInput v-model:value="model.nickname" :placeholder="$t('auth.nicknamePlaceholder')" />
    </NFormItem>

    <NFormItem path="password">
      <NInput
        v-model:value="model.password"
        type="password"
        show-password-on="click"
        :placeholder="$t('page.login.common.passwordPlaceholder')"
      />
    </NFormItem>

    <NFormItem path="confirmPassword">
      <NInput
        v-model:value="model.confirmPassword"
        type="password"
        show-password-on="click"
        :placeholder="$t('page.login.common.confirmPasswordPlaceholder')"
      />
    </NFormItem>

    <NFormItem path="sms_code">
      <SmsCode
        v-model="model.sms_code"
        :phone="model.phone"
        type="register"
      />
    </NFormItem>

    <NSpace vertical :size="18" class="w-full">
      <NButton type="primary" size="large" round block :loading="loading" @click="handleSubmit">
        {{ $t('common.confirm') }}
      </NButton>
      <NButton size="large" round block @click="toggleLoginModule('pwd-login')">
        {{ $t('page.login.common.back') }}
      </NButton>
    </NSpace>
  </NForm>
</template>

<style scoped></style>
