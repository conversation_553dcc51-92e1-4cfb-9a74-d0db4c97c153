<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import { useRouterPush } from '@/hooks/common/router';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { forgotPassword, resetPassword } from '@/service/api';
import { $t } from '@/locales';
import SmsCode from '@/components/common/sms-code.vue';

defineOptions({
  name: 'ResetPwd'
});

const { toggleLoginModule } = useRouterPush();
const { formRef, validate } = useNaiveForm();

// 重置密码步骤：1-获取重置令牌，2-重置密码
const step = ref<1 | 2>(1);
const resetToken = ref('');
const loading = ref(false);

interface FormModel {
  phone: string;
  sms_code: string;
  new_password: string;
  confirmPassword: string;
  reset_sms_code: string; // 第二步需要重新获取的验证码
}

const model: FormModel = reactive({
  phone: '',
  sms_code: '',
  new_password: '',
  confirmPassword: '',
  reset_sms_code: ''
});

type RuleRecord = Partial<Record<keyof FormModel, App.Global.FormRule[]>>;

const rules = computed<RuleRecord>(() => {
  const { formRules, createConfirmPwdRule } = useFormRules();

  const baseRules = {
    phone: formRules.phone,
    sms_code: [
      { required: true, message: $t('auth.smsCodeRequired') },
      { len: 6, message: $t('auth.smsCodeLength') }
    ]
  };

  if (step.value === 2) {
    return {
      ...baseRules,
      new_password: formRules.pwd,
      confirmPassword: createConfirmPwdRule(model.new_password),
      reset_sms_code: [
        { required: true, message: $t('auth.smsCodeRequired') },
        { len: 6, message: $t('auth.smsCodeLength') }
      ]
    };
  }

  return baseRules;
});

async function handleSubmit() {
  try {
    await validate();
    loading.value = true;

    if (step.value === 1) {
      // 第一步：获取重置令牌
      const { data } = await forgotPassword({
        phone: model.phone,
        sms_code: model.sms_code
      });

      resetToken.value = data.token;
      step.value = 2;
      window.$message?.success($t('auth.resetTokenObtained'));
    } else {
      // 第二步：重置密码
      await resetPassword({
        token: resetToken.value,
        new_password: model.new_password,
        sms_code: model.reset_sms_code
      });

      window.$message?.success($t('auth.passwordResetSuccess'));
      toggleLoginModule('pwd-login');
    }
  } catch (error) {
    // 错误已在拦截器中处理
  } finally {
    loading.value = false;
  }
}

function goBack() {
  if (step.value === 2) {
    step.value = 1;
  } else {
    toggleLoginModule('pwd-login');
  }
}
</script>

<template>
  <div>
    <!-- 步骤指示器 -->
    <div class="flex-center mb-24px">
      <NSteps :current="step" size="small">
        <NStep :title="$t('auth.verifyPhone')" />
        <NStep :title="$t('auth.resetPassword')" />
      </NSteps>
    </div>

    <NForm ref="formRef" :model="model" :rules="rules" size="large" :show-label="false" @keyup.enter="handleSubmit">
      <!-- 第一步：验证手机号 -->
      <template v-if="step === 1">
        <NFormItem path="phone">
          <NInput v-model:value="model.phone" :placeholder="$t('page.login.common.phonePlaceholder')" />
        </NFormItem>

        <NFormItem path="sms_code">
          <SmsCode
            v-model="model.sms_code"
            :phone="model.phone"
            type="reset"
          />
        </NFormItem>
      </template>

      <!-- 第二步：重置密码 -->
      <template v-if="step === 2">
        <NFormItem path="new_password">
          <NInput
            v-model:value="model.new_password"
            type="password"
            show-password-on="click"
            :placeholder="$t('auth.newPasswordPlaceholder')"
          />
        </NFormItem>

        <NFormItem path="confirmPassword">
          <NInput
            v-model:value="model.confirmPassword"
            type="password"
            show-password-on="click"
            :placeholder="$t('page.login.common.confirmPasswordPlaceholder')"
          />
        </NFormItem>

        <NFormItem path="reset_sms_code">
          <SmsCode
            v-model="model.reset_sms_code"
            :phone="model.phone"
            type="reset"
          />
        </NFormItem>
      </template>

      <NSpace vertical :size="18" class="w-full">
        <NButton type="primary" size="large" round block :loading="loading" @click="handleSubmit">
          {{ step === 1 ? $t('auth.getResetToken') : $t('auth.resetPassword') }}
        </NButton>
        <NButton size="large" round block @click="goBack">
          {{ $t('page.login.common.back') }}
        </NButton>
      </NSpace>
    </NForm>
  </div>
</template>

<style scoped></style>
